import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/models.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'hiking_app.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // 创建徒步记录表
    await db.execute('''
      CREATE TABLE hiking_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        location TEXT NOT NULL,
        date TEXT NOT NULL,
        description TEXT,
        imagePaths TEXT,
        distance REAL,
        duration REAL,
        elevation REAL,
        difficulty TEXT,
        isCompleted INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    // 创建用户信息表
    await db.execute('''
      CREATE TABLE user_profiles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        avatarPath TEXT,
        bio TEXT,
        birthDate TEXT,
        emergencyContact TEXT,
        emergencyPhone TEXT,
        preferences TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');
  }

  // 徒步记录相关操作
  Future<int> insertHikingRecord(HikingRecord record) async {
    final db = await database;
    return await db.insert('hiking_records', record.toMap());
  }

  Future<List<HikingRecord>> getAllHikingRecords() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'hiking_records',
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => HikingRecord.fromMap(maps[i]));
  }

  Future<List<HikingRecord>> getCompletedRecords() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'hiking_records',
      where: 'isCompleted = ?',
      whereArgs: [1],
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => HikingRecord.fromMap(maps[i]));
  }

  Future<List<HikingRecord>> getPlannedRecords() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'hiking_records',
      where: 'isCompleted = ?',
      whereArgs: [0],
      orderBy: 'date ASC',
    );
    return List.generate(maps.length, (i) => HikingRecord.fromMap(maps[i]));
  }

  Future<HikingRecord?> getHikingRecord(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'hiking_records',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return HikingRecord.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateHikingRecord(HikingRecord record) async {
    final db = await database;
    return await db.update(
      'hiking_records',
      record.toMap(),
      where: 'id = ?',
      whereArgs: [record.id],
    );
  }

  Future<int> deleteHikingRecord(int id) async {
    final db = await database;
    return await db.delete(
      'hiking_records',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // 用户信息相关操作
  Future<int> insertUserProfile(UserProfile profile) async {
    final db = await database;
    Map<String, dynamic> map = profile.toMap();
    map['preferences'] = jsonEncode(profile.preferences);
    return await db.insert('user_profiles', map);
  }

  Future<UserProfile?> getUserProfile() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_profiles',
      limit: 1,
    );
    if (maps.isNotEmpty) {
      Map<String, dynamic> map = Map.from(maps.first);
      if (map['preferences'] != null) {
        map['preferences'] = jsonDecode(map['preferences']);
      }
      return UserProfile.fromMap(map);
    }
    return null;
  }

  Future<int> updateUserProfile(UserProfile profile) async {
    final db = await database;
    Map<String, dynamic> map = profile.toMap();
    map['preferences'] = jsonEncode(profile.preferences);
    return await db.update(
      'user_profiles',
      map,
      where: 'id = ?',
      whereArgs: [profile.id],
    );
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
