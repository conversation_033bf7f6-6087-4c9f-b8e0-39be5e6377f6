import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/database_service.dart';

class UserProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  UserProfile? _userProfile;
  bool _isLoading = false;

  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  bool get hasProfile => _userProfile != null;

  Future<void> loadUserProfile() async {
    _isLoading = true;
    notifyListeners();

    try {
      _userProfile = await _databaseService.getUserProfile();
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> createUserProfile(UserProfile profile) async {
    try {
      final id = await _databaseService.insertUserProfile(profile);
      _userProfile = profile.copyWith(id: id);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      return false;
    }
  }

  Future<bool> updateUserProfile(UserProfile profile) async {
    try {
      await _databaseService.updateUserProfile(profile);
      _userProfile = profile;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }

  Future<bool> updateAvatar(String avatarPath) async {
    if (_userProfile == null) return false;
    
    final updatedProfile = _userProfile!.copyWith(avatarPath: avatarPath);
    return await updateUserProfile(updatedProfile);
  }

  Future<bool> updatePreference(String key, dynamic value) async {
    if (_userProfile == null) return false;
    
    final newPreferences = Map<String, dynamic>.from(_userProfile!.preferences);
    newPreferences[key] = value;
    
    final updatedProfile = _userProfile!.copyWith(preferences: newPreferences);
    return await updateUserProfile(updatedProfile);
  }

  dynamic getPreference(String key, [dynamic defaultValue]) {
    return _userProfile?.preferences[key] ?? defaultValue;
  }
}
