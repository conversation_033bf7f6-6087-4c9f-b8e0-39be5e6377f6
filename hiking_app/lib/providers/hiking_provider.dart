import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/database_service.dart';

class HikingProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  List<HikingRecord> _allRecords = [];
  List<HikingRecord> _completedRecords = [];
  List<HikingRecord> _plannedRecords = [];
  bool _isLoading = false;

  List<HikingRecord> get allRecords => _allRecords;
  List<HikingRecord> get completedRecords => _completedRecords;
  List<HikingRecord> get plannedRecords => _plannedRecords;
  bool get isLoading => _isLoading;

  // 获取最近的徒步记录（用于首页展示）
  List<HikingRecord> get recentRecords => 
      _completedRecords.take(5).toList();

  // 获取即将到来的计划（用于首页展示）
  List<HikingRecord> get upcomingPlans => 
      _plannedRecords.where((plan) => plan.date.isAfter(DateTime.now())).take(3).toList();

  Future<void> loadAllRecords() async {
    _isLoading = true;
    notifyListeners();

    try {
      _allRecords = await _databaseService.getAllHikingRecords();
      _completedRecords = await _databaseService.getCompletedRecords();
      _plannedRecords = await _databaseService.getPlannedRecords();
    } catch (e) {
      debugPrint('Error loading records: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addRecord(HikingRecord record) async {
    try {
      final id = await _databaseService.insertHikingRecord(record);
      final newRecord = record.copyWith(id: id);
      
      _allRecords.insert(0, newRecord);
      if (newRecord.isCompleted) {
        _completedRecords.insert(0, newRecord);
      } else {
        _plannedRecords.add(newRecord);
        _plannedRecords.sort((a, b) => a.date.compareTo(b.date));
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding record: $e');
      return false;
    }
  }

  Future<bool> updateRecord(HikingRecord record) async {
    try {
      await _databaseService.updateHikingRecord(record);
      
      // 更新本地列表
      final index = _allRecords.indexWhere((r) => r.id == record.id);
      if (index != -1) {
        _allRecords[index] = record;
      }

      // 更新分类列表
      _completedRecords.removeWhere((r) => r.id == record.id);
      _plannedRecords.removeWhere((r) => r.id == record.id);
      
      if (record.isCompleted) {
        _completedRecords.insert(0, record);
        _completedRecords.sort((a, b) => b.date.compareTo(a.date));
      } else {
        _plannedRecords.add(record);
        _plannedRecords.sort((a, b) => a.date.compareTo(b.date));
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating record: $e');
      return false;
    }
  }

  Future<bool> deleteRecord(int id) async {
    try {
      await _databaseService.deleteHikingRecord(id);
      
      _allRecords.removeWhere((r) => r.id == id);
      _completedRecords.removeWhere((r) => r.id == id);
      _plannedRecords.removeWhere((r) => r.id == id);
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting record: $e');
      return false;
    }
  }

  Future<HikingRecord?> getRecord(int id) async {
    try {
      return await _databaseService.getHikingRecord(id);
    } catch (e) {
      debugPrint('Error getting record: $e');
      return null;
    }
  }

  // 将计划标记为已完成
  Future<bool> markPlanAsCompleted(int id) async {
    final record = _allRecords.firstWhere((r) => r.id == id);
    final updatedRecord = record.copyWith(isCompleted: true);
    return await updateRecord(updatedRecord);
  }

  // 获取统计信息
  Map<String, dynamic> getStatistics() {
    final totalDistance = _completedRecords
        .where((r) => r.distance != null)
        .fold(0.0, (sum, r) => sum + r.distance!);
    
    final totalDuration = _completedRecords
        .where((r) => r.duration != null)
        .fold(0.0, (sum, r) => sum + r.duration!);

    return {
      'totalRecords': _completedRecords.length,
      'totalPlans': _plannedRecords.length,
      'totalDistance': totalDistance,
      'totalDuration': totalDuration,
      'averageDistance': _completedRecords.isNotEmpty ? totalDistance / _completedRecords.length : 0.0,
    };
  }
}
