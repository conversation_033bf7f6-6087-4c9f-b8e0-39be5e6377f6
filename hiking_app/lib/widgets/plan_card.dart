import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../theme/app_theme.dart';

class PlanCard extends StatelessWidget {
  final HikingRecord plan;
  final VoidCallback? onTap;

  const PlanCard({
    super.key,
    required this.plan,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final daysUntil = plan.date.difference(DateTime.now()).inDays;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.paddingMedium),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: AppTheme.cardRadius,
          boxShadow: const [AppTheme.cardShadow],
        ),
        child: Row(
          children: [
            // 左侧日期圆圈
            _buildDateCircle(daysUntil),
            const SizedBox(width: 16),
            // 中间内容
            Expanded(
              child: _buildContent(),
            ),
            // 右侧箭头
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppTheme.textLight,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateCircle(int daysUntil) {
    Color circleColor;
    String displayText;
    
    if (daysUntil < 0) {
      circleColor = Colors.red;
      displayText = '过期';
    } else if (daysUntil == 0) {
      circleColor = Colors.orange;
      displayText = '今天';
    } else if (daysUntil <= 7) {
      circleColor = Colors.green;
      displayText = '${daysUntil}天';
    } else {
      circleColor = AppTheme.primaryColor;
      displayText = '${daysUntil}天';
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            circleColor,
            circleColor.withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: circleColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          displayText,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          plan.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.location_on,
              size: 14,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                plan.location,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.access_time,
              size: 14,
              color: AppTheme.textLight,
            ),
            const SizedBox(width: 4),
            Text(
              DateFormat('yyyy年MM月dd日 HH:mm').format(plan.date),
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textLight,
              ),
            ),
          ],
        ),
        if (plan.description != null && plan.description!.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            plan.description!,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textLight,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }
}
