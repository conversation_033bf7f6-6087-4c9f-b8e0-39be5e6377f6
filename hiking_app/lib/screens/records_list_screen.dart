import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../theme/app_theme.dart';
import '../providers/hiking_provider.dart';
import '../models/models.dart';
import '../widgets/hiking_card.dart';
import 'add_record_screen.dart';

class RecordsListScreen extends StatefulWidget {
  final bool showPlans; // true显示计划，false显示记录

  const RecordsListScreen({super.key, this.showPlans = false});

  @override
  State<RecordsListScreen> createState() => _RecordsListScreenState();
}

class _RecordsListScreenState extends State<RecordsListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.showPlans ? 1 : 0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('我的徒步'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '已完成'),
            Tab(text: '计划'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRecordsList(true), // 已完成的记录
          _buildRecordsList(false), // 计划
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => AddRecordScreen(
                record: HikingRecord(
                  title: '',
                  location: '',
                  date: DateTime.now(),
                  isCompleted: _tabController.index == 0,
                ),
              ),
            ),
          );
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildRecordsList(bool showCompleted) {
    return Consumer<HikingProvider>(
      builder: (context, hikingProvider, child) {
        if (hikingProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final records = showCompleted
            ? hikingProvider.completedRecords
            : hikingProvider.plannedRecords;

        if (records.isEmpty) {
          return _buildEmptyState(
            showCompleted ? '还没有完成的徒步记录' : '还没有徒步计划',
            showCompleted ? '开始你的第一次徒步之旅吧！' : '创建你的第一个徒步计划吧！',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppTheme.paddingMedium),
          itemCount: records.length,
          itemBuilder: (context, index) {
            final record = records[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildRecordItem(record),
            );
          },
        );
      },
    );
  }

  Widget _buildRecordItem(HikingRecord record) {
    return GestureDetector(
      onTap: () => _showRecordDetails(record),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.paddingMedium),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: AppTheme.cardRadius,
          boxShadow: const [AppTheme.cardShadow],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        record.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 16,
                            color: AppTheme.textSecondary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            record.location,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(value, record),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('编辑'),
                        ],
                      ),
                    ),
                    if (!record.isCompleted)
                      const PopupMenuItem(
                        value: 'complete',
                        child: Row(
                          children: [
                            Icon(Icons.check),
                            SizedBox(width: 8),
                            Text('标记为完成'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('删除', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppTheme.textLight,
                ),
                const SizedBox(width: 4),
                Text(
                  DateFormat('yyyy年MM月dd日 HH:mm').format(record.date),
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textLight,
                  ),
                ),
                if (record.distance != null) ...[
                  const SizedBox(width: 16),
                  const Icon(
                    Icons.straighten,
                    size: 16,
                    color: AppTheme.textLight,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${record.distance!.toStringAsFixed(1)}km',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textLight,
                    ),
                  ),
                ],
                if (record.difficulty != null) ...[
                  const SizedBox(width: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(record.difficulty!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      record.difficulty!,
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            if (record.description != null && record.description!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                record.description!,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.hiking,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '简单':
        return Colors.green;
      case '中等':
        return Colors.orange;
      case '困难':
        return Colors.red;
      case '极难':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _showRecordDetails(HikingRecord record) {
    // TODO: 实现记录详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('查看 ${record.title} 的详情')),
    );
  }

  void _handleMenuAction(String action, HikingRecord record) async {
    final provider = context.read<HikingProvider>();
    
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AddRecordScreen(record: record),
          ),
        );
        break;
      case 'complete':
        final success = await provider.markPlanAsCompleted(record.id!);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已标记为完成'),
              backgroundColor: Colors.green,
            ),
          );
        }
        break;
      case 'delete':
        _showDeleteConfirmation(record);
        break;
    }
  }

  void _showDeleteConfirmation(HikingRecord record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除"${record.title}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await context.read<HikingProvider>().deleteRecord(record.id!);
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('删除成功'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
