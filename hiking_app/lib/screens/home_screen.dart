import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../theme/app_theme.dart';
import '../providers/hiking_provider.dart';
import '../providers/user_provider.dart';
import '../models/models.dart';
import '../widgets/hiking_card.dart';
import '../widgets/plan_card.dart';
import '../widgets/stats_card.dart';
import 'add_record_screen.dart';
import 'profile_screen.dart';
import 'records_list_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HikingProvider>().loadAllRecords();
      context.read<UserProvider>().loadUserProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            _buildGreetingSection(),
            _buildStatsSection(),
            _buildRecentRecordsSection(),
            _buildUpcomingPlansSection(),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 0,
      floating: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.paddingLarge),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '徒步记录',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            Consumer<UserProvider>(
              builder: (context, userProvider, child) {
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ProfileScreen(),
                      ),
                    );
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: const [AppTheme.cardShadow],
                    ),
                    child: userProvider.userProfile?.avatarPath != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: Image.asset(
                              userProvider.userProfile!.avatarPath!,
                              fit: BoxFit.cover,
                            ),
                          )
                        : const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 20,
                          ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGreetingSection() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(AppTheme.paddingLarge),
        child: Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final userName = userProvider.userProfile?.name ?? '徒步爱好者';
            final hour = DateTime.now().hour;
            String greeting;
            if (hour < 12) {
              greeting = '早上好';
            } else if (hour < 18) {
              greeting = '下午好';
            } else {
              greeting = '晚上好';
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$greeting，$userName',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '今天准备去哪里徒步呢？',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.paddingLarge),
        child: Consumer<HikingProvider>(
          builder: (context, hikingProvider, child) {
            final stats = hikingProvider.getStatistics();
            return Row(
              children: [
                Expanded(
                  child: StatsCard(
                    title: '总记录',
                    value: '${stats['totalRecords']}',
                    subtitle: '次徒步',
                    gradient: AppTheme.cardGradient,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: StatsCard(
                    title: '总距离',
                    value: '${stats['totalDistance'].toStringAsFixed(1)}',
                    subtitle: '公里',
                    gradient: AppTheme.planGradient,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildRecentRecordsSection() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(AppTheme.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '最近记录',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const RecordsListScreen(showPlans: false),
                      ),
                    );
                  },
                  child: const Text('查看全部'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<HikingProvider>(
              builder: (context, hikingProvider, child) {
                if (hikingProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                final recentRecords = hikingProvider.recentRecords;
                if (recentRecords.isEmpty) {
                  return _buildEmptyState('还没有徒步记录', '开始你的第一次徒步之旅吧！');
                }

                return SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: recentRecords.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.only(
                          right: index < recentRecords.length - 1 ? 16 : 0,
                        ),
                        child: HikingCard(record: recentRecords[index]),
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingPlansSection() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(AppTheme.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '即将到来',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const RecordsListScreen(showPlans: true),
                      ),
                    );
                  },
                  child: const Text('查看全部'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<HikingProvider>(
              builder: (context, hikingProvider, child) {
                final upcomingPlans = hikingProvider.upcomingPlans;
                if (upcomingPlans.isEmpty) {
                  return _buildEmptyState('暂无计划', '创建你的下一次徒步计划吧！');
                }

                return Column(
                  children: upcomingPlans.map((plan) => 
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: PlanCard(plan: plan),
                    ),
                  ).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: AppTheme.cardRadius,
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.hiking,
              size: 32,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AddRecordScreen(),
          ),
        );
      },
      backgroundColor: AppTheme.primaryColor,
      icon: const Icon(Icons.add, color: Colors.white),
      label: const Text(
        '添加记录',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
      ),
    );
  }
}
