class UserProfile {
  final int? id;
  final String name;
  final String? email;
  final String? phone;
  final String? avatarPath;
  final String? bio;
  final DateTime? birthDate;
  final String? emergencyContact;
  final String? emergencyPhone;
  final Map<String, dynamic> preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    this.id,
    required this.name,
    this.email,
    this.phone,
    this.avatarPath,
    this.bio,
    this.birthDate,
    this.emergencyContact,
    this.emergencyPhone,
    this.preferences = const {},
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // 从Map创建对象（用于数据库读取）
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      phone: map['phone'],
      avatarPath: map['avatarPath'],
      bio: map['bio'],
      birthDate: map['birthDate'] != null ? DateTime.parse(map['birthDate']) : null,
      emergencyContact: map['emergencyContact'],
      emergencyPhone: map['emergencyPhone'],
      preferences: map['preferences'] != null 
          ? Map<String, dynamic>.from(map['preferences'] as Map)
          : {},
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  // 转换为Map（用于数据库存储）
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatarPath': avatarPath,
      'bio': bio,
      'birthDate': birthDate?.toIso8601String(),
      'emergencyContact': emergencyContact,
      'emergencyPhone': emergencyPhone,
      'preferences': preferences,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // 复制对象并修改某些字段
  UserProfile copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? avatarPath,
    String? bio,
    DateTime? birthDate,
    String? emergencyContact,
    String? emergencyPhone,
    Map<String, dynamic>? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatarPath: avatarPath ?? this.avatarPath,
      bio: bio ?? this.bio,
      birthDate: birthDate ?? this.birthDate,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'UserProfile{id: $id, name: $name, email: $email}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfile &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
