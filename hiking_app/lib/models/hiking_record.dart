class HikingRecord {
  final int? id;
  final String title;
  final String location;
  final DateTime date;
  final String? description;
  final List<String> imagePaths;
  final double? distance; // 距离（公里）
  final double? duration; // 持续时间（小时）
  final double? elevation; // 海拔（米）
  final String? difficulty; // 难度等级
  final bool isCompleted; // 是否已完成（用于区分记录和计划）
  final DateTime createdAt;
  final DateTime updatedAt;

  HikingRecord({
    this.id,
    required this.title,
    required this.location,
    required this.date,
    this.description,
    this.imagePaths = const [],
    this.distance,
    this.duration,
    this.elevation,
    this.difficulty,
    this.isCompleted = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // 从Map创建对象（用于数据库读取）
  factory HikingRecord.fromMap(Map<String, dynamic> map) {
    return HikingRecord(
      id: map['id'],
      title: map['title'],
      location: map['location'],
      date: DateTime.parse(map['date']),
      description: map['description'],
      imagePaths: map['imagePaths'] != null 
          ? List<String>.from(map['imagePaths'].split(',').where((path) => path.isNotEmpty))
          : [],
      distance: map['distance']?.toDouble(),
      duration: map['duration']?.toDouble(),
      elevation: map['elevation']?.toDouble(),
      difficulty: map['difficulty'],
      isCompleted: map['isCompleted'] == 1,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  // 转换为Map（用于数据库存储）
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'location': location,
      'date': date.toIso8601String(),
      'description': description,
      'imagePaths': imagePaths.join(','),
      'distance': distance,
      'duration': duration,
      'elevation': elevation,
      'difficulty': difficulty,
      'isCompleted': isCompleted ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // 复制对象并修改某些字段
  HikingRecord copyWith({
    int? id,
    String? title,
    String? location,
    DateTime? date,
    String? description,
    List<String>? imagePaths,
    double? distance,
    double? duration,
    double? elevation,
    String? difficulty,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HikingRecord(
      id: id ?? this.id,
      title: title ?? this.title,
      location: location ?? this.location,
      date: date ?? this.date,
      description: description ?? this.description,
      imagePaths: imagePaths ?? this.imagePaths,
      distance: distance ?? this.distance,
      duration: duration ?? this.duration,
      elevation: elevation ?? this.elevation,
      difficulty: difficulty ?? this.difficulty,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'HikingRecord{id: $id, title: $title, location: $location, date: $date, isCompleted: $isCompleted}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HikingRecord &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
