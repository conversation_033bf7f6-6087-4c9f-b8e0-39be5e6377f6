[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86_64", "file_": "/Users/<USER>/.fvm/versions/3.32.6/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Documents/GitHub/hiking/hiking_app/build/.cxx/Debug/4l5w3z67/x86_64/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/.fvm/versions/3.32.6/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/.fvm/versions/3.32.6/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]