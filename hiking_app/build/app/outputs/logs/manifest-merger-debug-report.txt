-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:2:5-33:19
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
MERGED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:1:1-45:12
MERGED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:1:1-45:12
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/3c584b10333d42ed272059e41fd6685f/transformed/jetified-activity-1.10.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/58a0f53fd92f6561a1dd451c29da9990/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.12/transforms/17952863fa1b6f5dddf3dbb6f4ce2941/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/25a37e5cf0f4e5220cbf7cafe9249990/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3b106ec6394803c577492ce2f65d8645/transformed/jetified-core-viewtree-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:1:11-69
queries
ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:41:13-72
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:41:21-70
data
ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/main/AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml:6:5-66
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
MERGED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/3c584b10333d42ed272059e41fd6685f/transformed/jetified-activity-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/3c584b10333d42ed272059e41fd6685f/transformed/jetified-activity-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/58a0f53fd92f6561a1dd451c29da9990/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/58a0f53fd92f6561a1dd451c29da9990/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.12/transforms/17952863fa1b6f5dddf3dbb6f4ce2941/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.12/transforms/17952863fa1b6f5dddf3dbb6f4ce2941/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/25a37e5cf0f4e5220cbf7cafe9249990/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/25a37e5cf0f4e5220cbf7cafe9249990/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3b106ec6394803c577492ce2f65d8645/transformed/jetified-core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3b106ec6394803c577492ce2f65d8645/transformed/jetified-core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/hiking/hiking_app/android/app/src/debug/AndroidManifest.xml
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/GitHub/hiking/hiking_app/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.hiking_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.hiking_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/09d12e707b508dd64098ad851ea0c787/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
