{"buildFiles": ["/Users/<USER>/.fvm/versions/3.32.6/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/GitHub/hiking/hiking_app/build/.cxx/Debug/4l5w3z67/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/GitHub/hiking/hiking_app/build/.cxx/Debug/4l5w3z67/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}