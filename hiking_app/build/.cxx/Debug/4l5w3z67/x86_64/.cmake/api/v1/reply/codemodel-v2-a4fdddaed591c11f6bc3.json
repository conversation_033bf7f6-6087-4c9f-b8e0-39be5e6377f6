{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/GitHub/hiking/hiking_app/build/.cxx/Debug/4l5w3z67/x86_64", "source": "/Users/<USER>/.fvm/versions/3.32.6/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}