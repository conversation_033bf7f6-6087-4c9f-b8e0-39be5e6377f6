# 徒步记录应用

一个优美的Flutter徒步记录应用，帮助用户记录和规划徒步活动。

## 功能特性

### 🏔️ 徒步记录
- 记录徒步标题、地点、时间
- 上传多张照片
- 记录距离、时长、海拔、难度等详细信息
- 添加个人描述和感想

### 📅 徒步计划
- 创建未来的徒步计划
- 计划到期提醒
- 将计划标记为已完成

### 👤 个人信息
- 个人资料管理
- 头像设置
- 紧急联系人信息
- 徒步统计数据展示

### 🎨 优美UI
- 参考现代设计风格
- 渐变色彩搭配
- 卡片式布局
- 流畅的动画效果

## 技术栈

- **Flutter** - 跨平台移动应用开发框架
- **Provider** - 状态管理
- **SQLite** - 本地数据存储
- **Image Picker** - 图片选择和拍照
- **Intl** - 国际化和日期格式化

## 安装和运行

1. 确保已安装Flutter SDK
2. 克隆项目到本地
3. 进入项目目录：
   ```bash
   cd hiking_app
   ```
4. 安装依赖：
   ```bash
   flutter pub get
   ```
5. 运行应用：
   ```bash
   flutter run
   ```

## 主要功能

1. **智能分类**：自动区分已完成的记录和未来的计划
2. **统计分析**：展示总徒步次数、总距离等统计信息
3. **图片管理**：支持多图片上传和管理
4. **数据持久化**：使用SQLite本地存储，无需网络连接
5. **响应式设计**：适配不同屏幕尺寸

## 许可证

MIT License
